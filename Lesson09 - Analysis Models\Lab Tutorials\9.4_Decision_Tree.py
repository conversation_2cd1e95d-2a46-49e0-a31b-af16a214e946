#!/usr/bin/env python
# coding: utf-8

# # Classification Model 2: Decision Tree
# 
# ***
# ### Loading Dataset

# In[1]:


from google.colab import drive

drive.mount('/content/drive', force_remount=True)


# In[2]:


ROOT_DIR = '/content/drive/MyDrive/C3790C'


# In[ ]:


# Import necessary Python libraries here ...
# ...
# ...
# ...

# Loading the dataset from the /data folder here
data_path = None  # TODO: Specify the path to your dataset

# Read your csv file here ...
currentdf = None  # TODO: Read your CSV file using pandas

# Allocate your training data and label
x = None  # TODO: Assign your feature variables
y = None  # TODO: Assign your target variable

# Splitting dataset into 75% for training and 25% for testing here ...
# ...
# ...

# Display the features and label from the training set
print("Features shape:", None)  # TODO: Print X_train shape
print("Labels shape:", None)    # TODO: Print y_train shape

# Insert code to standardize your dataset here ...
# ...
# ...


# ## Building a Decision Tree Model
# 
# Let's train a model using the selected features extracted earlier and set the 'max_depth' parameter to 3.
# 

# In[ ]:


from sklearn.tree import DecisionTreeClassifier
class_tree = DecisionTreeClassifier(max_depth=3)  # TODO: Set appropriate max_depth
class_tree.fit(X_train, y_train)


# We vistualize the classification tree by importing 'export_graphviz', which exports the decision tree in a file with DOT format. This function generates a GraphiViz representation of the decision tree, which is then written into 'out_file'. Lasty, the image function is used to display the tree.

# In[ ]:


# Import necessary libraries
# ...


# The code to display the graphical representation is as follows:

# In[ ]:


dot_data = StringIO()
export_graphviz(decision_tree=class_tree,  # TODO: Use your trained decision tree model
                out_file=dot_data,
                rounded=True,
                feature_names = X_train.columns,
                class_names = ['CottonWash', 'CottonRinse', 'CottonSpin', 'BeddingWash', 'BeddingRinse', 'BeddingSpin',
                 'DailyWash', 'DailyRinse','DailySpin'],
                special_characters=True)
graph = pydotplus.graph_from_dot_data(dot_data.getvalue())
Image(graph.create_png())


# Next, print the 'proportions' by setting the parameter for $proportion=True$.
# 

# In[ ]:


dot_data = StringIO()
export_graphviz(decision_tree=class_tree,  # TODO: Use your trained decision tree model
                out_file=dot_data,
                rounded=True,
                proportion=True,  # TODO: Set proportion parameter
                feature_names = X_train.columns,
                class_names = ['CottonWash', 'CottonRinse', 'CottonSpin', 'BeddingWash', 'BeddingRinse', 'BeddingSpin',
                 'DailyWash', 'DailyRinse','DailySpin'],
                special_characters=True)
graph = pydotplus.graph_from_dot_data(dot_data.getvalue())
Image(graph.create_png())


# Has the tree structure changed? Are you getting a different result now? Explain your observation.

# ...
# 

# ## Training a Large Classification Tree
# 
# Let's train a larger tree by modifying the 'max_depth' to 6 and 'min_samples_split' to 50 in the scikit-learn parameters. You may also refer to https://scikit-learn.org/stable/modules/generated/sklearn.tree.DecisionTreeClassifier.html for other types of parameters.

# In[ ]:


class_tree = DecisionTreeClassifier(max_depth=6, min_samples_split=50)  # TODO: Set appropriate parameters
class_tree.fit(X_train, y_train)  # TODO: Use your training data
y_pred_class_tree = class_tree.predict(X_test)  # TODO: Use your test data


# We can calculate the training accuracy score of this model as follows:

# In[ ]:


from sklearn.metrics import accuracy_score
accuracy_class_tree = accuracy_score(y_true=y_test, y_pred=y_pred_class_tree)  # TODO: Use your actual test labels and predictions
accuracy_class_tree


# We get 0.89133 or 89.1% accuracy for this $Decison Tree$ model by using the training data, which is much higher than the KNN model (81.4%).
# 

# Another nice feature of this model is that we can get a normalizd score of "important" features by using the 'feature_importances_' method.

# In[ ]:


pd.Series(data=class_tree.feature_importances_, index=X_train.columns).sort_values(ascending=False).round(3)


# You can also compare these values using a bar graph.

# In[ ]:


pd.Series(data=class_tree.feature_importances_, index=X_train.columns).sort_values(ascending=False).plot(kind='bar');


# Discuss with your team what can you tell from these feature importance?

# Your response here ...
# 
# 

# ## Model Evaluation
# 
# ### Confusion Matrix
# 
# Let's compute the confusion matrix for this Decision Tree model for the laudromat use case.
# 

# In[ ]:


from sklearn.metrics import confusion_matrix

# Determine the accuracy of the model using test data
score = class_tree.score(X_test, y_test)  # TODO: Use your test data

# Provide the necessary label
class_label = ['CottonWash', 'CottonRinse', 'CottonSpin', 'BeddingWash', 'BeddingRinse', 'BeddingSpin', 'DailyWash', 'DailyRinse','DailySpin']  # TODO: Define your class labels

class_tree.fit(X_train, y_train)  # TODO: Use your training data
cm = confusion_matrix(y_test, class_tree.predict(X_test), labels=class_label)  # TODO: Use your test data and labels

axes = sns.heatmap(cm, square=True, annot=True, fmt ='d', cbar=True, cmap=plt.cm.GnBu)
axes.set_ylabel('Actual')
axes.set_ylabel('Predication')
tick_marks = np.arange(len(class_label)) + 0.5
axes.set_xticks(tick_marks)
axes.set_xticklabels(class_label, rotation = 90)
axes.set_yticks(tick_marks)
axes.set_yticklabels(class_label, rotation = 0)
axes.set_title('Confusion Matrix')
plt.show()


# Let's calculate the accuracy, precision, recall and F1 Score for our Decision Tree model using the test dataset.
# 

# In[ ]:


from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score

class_tree = DecisionTreeClassifier(max_depth=6, min_samples_split=50)  # TODO: Set appropriate parameters
class_tree.fit(X_train, y_train)  # TODO: Use your training data

# Get prediction using test data
y_pred_test = class_tree.predict(X_test)  # TODO: Use your test data

# Calculate the relevant metrics
accuracy = accuracy_score(y_test, y_pred_test)  # TODO: Use your actual test labels and predictions
precision = precision_score(y_test, y_pred_test, average='macro')  # TODO: Use your actual test labels and predictions
recall = recall_score(y_test, y_pred_test, average='macro')  # TODO: Use your actual test labels and predictions
f1 = f1_score(y_test, y_pred_test, average='macro')  # TODO: Use your actual test labels and predictions

print ("Accuracy: {:0.1f}%, Precision: {:0.1f}%, Recall: {:0.1f}%, F1 Score: {:0.1f}%".format(100*accuracy, 100* precision, 100*recall, 100*f1))


# Are the accuracy, precision, recall and F1 Score for Decision Tree model better or worst then your KNN model? Explain.
# 

# Your response here ...
# 

# Lastly, you may save your Decision Tree model for future comparison.
# 

# In[ ]:


import pickle as pk

model_filename= ROOT_DIR + "/model/dt.mdl"
with open(model_filename, "wb") as file:
    pk.dump(class_tree, file)  # TODO: Save your trained model
print("Model Saved")


# ***
'''
The selected code is actually a documentation comment block that was added to the end of the file. 

Here's what each part does:

Code Analysis:

from sklearn.tree import DecisionTreeClassifier
Imports the Decision Tree classifier from scikit-learn library

This is a machine learning algorithm used for classification tasks
class_tree = DecisionTreeClassifier(max_depth=3)

Creates a Decision Tree classifier object with a maximum depth of 3 levels
max_depth=3 limits how deep the tree can grow to prevent overfitting

The variable class_tree stores this classifier instance
class_tree.fit(X_train, y_train)

Trains the decision tree model using training data
X_train contains the input features (independent variables)
y_train contains the target labels (dependent variable)

The .fit() method teaches the model to recognize patterns
The documentation text explains:
The file has been fixed and is syntactically correct
Students need to define their own variables (X_train, y_train, etc.)

The command to check if the Python file compiles correctly
Purpose:

This is a machine learning tutorial where students learn to:

Build a Decision Tree classification model
Train it on laundromat usage data (based on the class names in the file)
Predict washing machine cycles (Cotton, Bedding, Daily wash/rinse/spin)
The comment block serves as documentation about the fixes that were made to resolve syntax errors in the tutorial file.
'''

'''
from sklearn.tree import DecisionTreeClassifier
class_tree = DecisionTreeClassifier(max_depth=3)  
# TODO: Set appropriate max_depth
class_tree.fit(X_train, y_train)

The file is now syntactically correct and ready for students to fill in their specific data and parameters. The remaining "undefined variable" warnings are expected since this is a tutorial template where students need to define their own variables like X_train, y_train, X_test, y_test, etc.

To run this code:
 python -m py_compile "Lesson09 - Analysis Models\Lab Tutorials\9.4_Decision_Tree.py"
'''