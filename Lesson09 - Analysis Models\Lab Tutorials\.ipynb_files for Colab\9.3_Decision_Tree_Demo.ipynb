{"cells": [{"cell_type": "markdown", "id": "a46df0cd", "metadata": {"id": "a46df0cd"}, "source": ["# Lab Demonstration - Decision Tree\n", "****\n", "\n", "Classification trees are popular because they are transparent, straightforward and easy to understand how they produce the predictions. They can be used for regression or classification tasks. They produce the predictions by creating a series of rules that applied consecutively until they arrive at a \"leaf\" node in the tree that contains the classification.\n", "\n", "![DecisionTree.png](attachment:DecisionTree.png)"]}, {"cell_type": "markdown", "id": "d1c2d466", "metadata": {"id": "d1c2d466"}, "source": ["\n", "## Decision Tree Classifier\n", "\n", "### Defining Dataset\n", "\n", "Let's create a dataset with two features (in blue ) and one label (in orange) that contain a record of weather, temperature and the decision to play tennis from the past 14 days. This dataset will be used to build a model to predict whether the weather and temperature today is suitable for you to play a tennis match outdoor.\n", "\n", "![table-3.png](attachment:table-3.png)\n"]}, {"cell_type": "code", "execution_count": 1, "id": "9148d21c", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "9148d21c", "executionInfo": {"status": "ok", "timestamp": 1749716736458, "user_tz": -480, "elapsed": 23135, "user": {"displayName": "<PERSON>", "userId": "08280565725279349310"}}, "outputId": "bb86c4bf-b5eb-4313-d436-6005685e8c5f"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Mounted at /content/drive\n"]}], "source": ["from google.colab import drive\n", "\n", "drive.mount('/content/drive', force_remount=True)"]}, {"cell_type": "code", "execution_count": 2, "id": "967e9b39", "metadata": {"id": "967e9b39", "executionInfo": {"status": "ok", "timestamp": 1749716738481, "user_tz": -480, "elapsed": 11, "user": {"displayName": "<PERSON>", "userId": "08280565725279349310"}}}, "outputs": [], "source": ["ROOT_DIR = '/content/drive/MyDrive/C3790C'"]}, {"cell_type": "code", "execution_count": 3, "id": "41b6909a", "metadata": {"id": "41b6909a", "executionInfo": {"status": "ok", "timestamp": 1749716741076, "user_tz": -480, "elapsed": 10, "user": {"displayName": "<PERSON>", "userId": "08280565725279349310"}}}, "outputs": [], "source": ["# First Feature\n", "weather = ['<PERSON>','<PERSON>','Overcast','Rainy','Rainy','Rainy','Overcast','<PERSON>','<PERSON>',\n", "'Rainy','<PERSON>','Overcast','Overcast','Rainy']\n", "\n", "# Second Feature\n", "temp = ['Hot','Hot','Hot','Mild','Cool','Cool','Cool','Mild','Cool','Mild','Mild','Mild','Hot','Mild']\n", "\n", "# Label or target varible\n", "tennis = ['No','No','Yes','Yes','Yes','No','Yes','No','Yes','Yes','Yes','Yes','Yes','No']\n"]}, {"cell_type": "markdown", "id": "8489462d", "metadata": {"id": "8489462d"}, "source": ["### Encoding data columns\n", "\n", "Various machine learning algorithms require numerical input data, so you need to represent categorical columns in a numerical column.\n", "\n", "In order to encode this data, you could map each value to a number. e.g. Overcast:0, <PERSON><PERSON>:1, and <PERSON>:2.\n", "\n", "This process is known as label encoding, and $sklearn$ conveniently will do this for you using $Label Encoder$.\n"]}, {"cell_type": "code", "execution_count": null, "id": "44316899", "metadata": {"id": "44316899", "outputId": "b1e7ef39-2c09-4290-98ab-cce761b1a7e1"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[2 2 0 1 1 1 0 2 2 1 2 0 0 1]\n"]}], "source": ["# Importing preprocessing library\n", "from sklearn import preprocessing\n", "\n", "# Creating a labelEncoder\n", "le = preprocessing.LabelEncoder()\n", "\n", "# Converting string labels into numbers.\n", "weather_encoded = le.fit_transform(weather)\n", "print (weather_encoded)\n"]}, {"cell_type": "markdown", "id": "a89c0027", "metadata": {"id": "a89c0027"}, "source": ["Here, you have imported preprocessing module and created Label Encoder object. Using this $LabelEncoder$ object, you can fit and transform \"weather\" column into the numeric column. Similarly, you can encode temperature and label into numeric columns.\n"]}, {"cell_type": "code", "execution_count": null, "id": "2e2d44b1", "metadata": {"id": "2e2d44b1"}, "outputs": [], "source": ["# Converting string labels into numbers\n", "temp_encoded = le.fit_transform(temp)\n", "label = le.fit_transform(tennis)\n"]}, {"cell_type": "markdown", "id": "bec433cc", "metadata": {"id": "bec433cc"}, "source": ["### Combining Features\n", "\n", "You can combine multiple columns or features into a single set of data using \"zip\" function.\n"]}, {"cell_type": "code", "execution_count": null, "id": "69aa243e", "metadata": {"id": "69aa243e"}, "outputs": [], "source": ["# Combining weather and temp into a single list of tuples\n", "features = list(zip(weather_encoded, temp_encoded))\n"]}, {"cell_type": "markdown", "id": "71fb0166", "metadata": {"id": "71fb0166"}, "source": ["### Generating Model\n", "\n", "Let's build Decision Tree classifier model.\n", "\n", "First, import the $from sklearn.tree import DecisionTreeClassifier$ module and set the $max depth$ parameter to 3.\n"]}, {"cell_type": "code", "execution_count": null, "id": "b2addcac", "metadata": {"id": "b2addcac", "outputId": "5bbdaacc-2a43-4286-f265-cef8f43a69ec"}, "outputs": [{"data": {"text/plain": ["DecisionTreeClassifier(max_depth=3)"]}, "execution_count": 38, "metadata": {}, "output_type": "execute_result"}], "source": ["from sklearn.tree import DecisionTreeClassifier\n", "class_tree = DecisionTreeClassifier(max_depth=3)\n", "class_tree.fit(features, label)\n"]}, {"cell_type": "markdown", "id": "6254ad26", "metadata": {"id": "6254ad26"}, "source": ["We start vistualizing the classification tree by importing 'export_graphviz', which exports the decision tree in a file with DOT format. This function generates a GraphiViz representation of the decision tree, which is then written into 'out_file'. Finally, the image function is used to display the tree. You may refer to https://www.graphviz.org/ for more information on GraphViz.\n"]}, {"cell_type": "code", "execution_count": null, "id": "32f692a3", "metadata": {"id": "32f692a3"}, "outputs": [], "source": ["from six import StringIO\n", "from sklearn.tree import export_graphviz\n", "from IPython.display import Image\n", "import pydotplus\n"]}, {"cell_type": "markdown", "id": "7a9b731a", "metadata": {"id": "7a9b731a"}, "source": ["The code to display the graphical representation is as follows:"]}, {"cell_type": "code", "execution_count": null, "id": "61cef67a", "metadata": {"id": "61cef67a", "outputId": "c00ef311-a6f1-46f4-b36d-6f21790e9c39"}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<IPython.core.display.Image object>"]}, "execution_count": 40, "metadata": {}, "output_type": "execute_result"}], "source": ["dot_data = StringIO()\n", "export_graphviz(decision_tree=class_tree,\n", "                out_file=dot_data,\n", "                rounded=True,\n", "                feature_names = (\"weather_encoded\", \"temp_encoded\"),\n", "                class_names = [\"Play\", \"Don't Play\"],\n", "                special_characters=True)\n", "graph = pydotplus.graph_from_dot_data(dot_data.getvalue())\n", "Image(graph.create_png())\n"]}, {"cell_type": "markdown", "id": "2f90cc74", "metadata": {"id": "2f90cc74"}, "source": ["The above diagram generated is the graphical representation of the rules produced by the $Decision Tree$ model. For every observation, start at the top and based on the truth value of the proposition, go left if it is True and right if it is False. Suppose you have the data for one observation, then you start answering in the following sequence:\n", "\n", "1. Is weather_encoded <= 0.5? Go right if the answer is False\n", "2. Is temp_encoded <011.5? Go left if the answer is True\n", "3. Is weather_encoded <= 1.5? If the answer is False, go right and predict that the weather and temperature is bad, so you may advice the players not to play tennis.\n", "\n", "Next, you may find it more useful to print the 'proportions' instead of the 'counts' by setting the parameter for $proportion=True$."]}, {"cell_type": "code", "execution_count": null, "id": "2cdb2cbb", "metadata": {"id": "2cdb2cbb", "outputId": "fb11cffe-5adc-4c5e-fd7f-6ecd76abd1bb"}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<IPython.core.display.Image object>"]}, "execution_count": 41, "metadata": {}, "output_type": "execute_result"}], "source": ["dot_data = StringIO()\n", "export_graphviz(decision_tree=class_tree,\n", "                out_file=dot_data,\n", "                rounded=True,\n", "                proportion=True,\n", "                feature_names = (\"weather_encoded\", \"temp_encoded\"),\n", "                class_names = [\"Play\", \"Don't Play\"],\n", "                special_characters=True)\n", "graph = pydotplus.graph_from_dot_data(dot_data.getvalue())\n", "Image(graph.create_png())\n"]}, {"cell_type": "markdown", "id": "f2f88c1f", "metadata": {"id": "f2f88c1f"}, "source": ["As you can see in the leftmost leaf node, only 14.3% of the samples are 'Play', compared to the rightmost node where 35.7% are 'Don't Play'.\n", "\n", "Let's modify your Python code by assigning the $ max depth$ of tree to 1\n"]}, {"cell_type": "code", "execution_count": null, "id": "5133b4bd", "metadata": {"id": "5133b4bd", "outputId": "028c5757-c08b-4f23-da98-9e73a5548e0c"}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<IPython.core.display.Image object>"]}, "execution_count": 42, "metadata": {}, "output_type": "execute_result"}], "source": ["# Rewrite your Python code here...\n", "dot_data = StringIO()\n", "export_graphviz(decision_tree=class_tree,\n", "                max_depth=1,\n", "                out_file=dot_data,\n", "                rounded=True,\n", "                proportion=True,\n", "                feature_names = (\"weather_encoded\", \"temp_encoded\"),\n", "                class_names = [\"Play\", \"Don't Play\"],\n", "                special_characters=True)\n", "graph = pydotplus.graph_from_dot_data(dot_data.getvalue())\n", "Image(graph.create_png())\n"]}, {"cell_type": "markdown", "id": "64d9aa27", "metadata": {"id": "64d9aa27"}, "source": ["### The Pros and Cons of using Decision Trees\n", "\n", "#### Advantages\n", "- They are very easy to understand and explain\n", "- The rules produced are easy to implement\n", "- It is computationally efficient to produce predictions with them\n", "- As shown above, there is very little pre-processing needed. The classification trees are not affected if the predictors are skewed (unbalanced), or being in different scales.\n", "\n", "#### Drawbacks\n", "- Their predictive power is often less than that of other models. Hence, you would not expect highly accurate predictions.\n", "- They may be unstable. For example, small changes in the dataset can lead to very different rules.\n", "- They can be overfit.\n", "- Due to the simplicity of the if-else rules, there are some complex interactions that these models cannot learn.\n", "\n", "You may refer to https://scikit-learn.org/stable/modules/tree.html#tree for some useful tips on practical usage.\n"]}, {"cell_type": "markdown", "id": "426d03b8", "metadata": {"id": "426d03b8"}, "source": ["Assume today's weather is overcast and the temperature is mild, let's test the outcome of the Decision Tree model to predict wether you should still play tennis today."]}, {"cell_type": "code", "execution_count": null, "id": "4d82d680", "metadata": {"id": "4d82d680", "outputId": "c13f3938-741c-446f-e41b-6b0348f6d02c"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[1]\n"]}], "source": ["# Predict the Output\n", "predicted = class_tree.predict([[0, 2]]) # 0:Overcast, 2:Mild\n", "print (predicted)"]}, {"cell_type": "markdown", "id": "755aa3eb", "metadata": {"id": "755aa3eb"}, "source": ["In the above demonstration, you have given input [0, 2], where 0 means Overcast weather and 2 means Mild temperature. The Decision Tree model has predicts [1], which means today's weather and temperature is suitable for you to play tennis.\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}, "colab": {"provenance": []}}, "nbformat": 4, "nbformat_minor": 5}