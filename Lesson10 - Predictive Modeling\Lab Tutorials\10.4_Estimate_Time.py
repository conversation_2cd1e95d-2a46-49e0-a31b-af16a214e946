#!/usr/bin/env python
# coding: utf-8

# ## Estimate the Completion Time of the Washing Cycle
# 
# 
# Given that a new data with the respective parameters shown below were captured in the washing machine (see 'input1.csv' file):  
# 
# ![new_data1.png](attachment:new_data1.png)
# 
# We shall use the predictive model trained earlier to predict the wash cycle, and estimate the time needed for the washing machine to complete the entire washing. The tasks to be executed are as follows:
# 
# 1. Load this new data from the 'new_data1.csv' file
# 2. Load the predictive model that produced the best evaluation result earlier
# 3. Predict the wash cycle from the 'best' model
# 4. Calculate the estimated time needed to wait before the sequence of washing is completed
# 
# ***

# In[1]:

# Note: This script was adapted from Google Colab to run locally
# from google.colab import drive
# drive.mount('/content/drive', force_remount=True)

# In[2]:

# Updated for local environment - using Lab Tutorials directory
import os
# Get the directory where this script is located
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
ROOT_DIR = SCRIPT_DIR  # Use script directory as root


# In[ ]:


# Step 1: Input necessary Python libraries
import pandas as pd

# Loading the data from new input
data_path  = os.path.join(ROOT_DIR, 'data', 'input1.csv')
currentdf  = pd.read_csv(data_path)

input1 = currentdf.iloc[:, 1:]
print (input1)


# In[ ]:


# Step 2: Load the predictive model that has produced the best result earlier
import pickle as pk

model_filename = os.path.join(ROOT_DIR, "model", "best_model.mdl")
scaler_filename = os.path.join(ROOT_DIR, "model", "scaler.pkl")

# Check if model file exists
if not os.path.exists(model_filename):
    print(f"Error: Model file not found at {model_filename}")
    print("Please ensure you have trained and saved a model first using the previous lab tutorials.")
    print("Available files in model directory:")
    model_dir = os.path.join(ROOT_DIR, "model")
    if os.path.exists(model_dir):
        for file in os.listdir(model_dir):
            print(f"  - {file}")
    else:
        print("  (model directory does not exist)")
    exit(1)

# Load the model and scaler
model = pk.load(open(model_filename, 'rb'))
print("Model loaded")

# Load the scaler if it exists (needed for proper preprocessing)
if os.path.exists(scaler_filename):
    scaler = pk.load(open(scaler_filename, 'rb'))
    print("Scaler loaded")
    # Apply the same preprocessing as during training
    input1_scaled = scaler.transform(input1)
else:
    print("Warning: Scaler not found. Using unscaled data.")
    input1_scaled = input1


# In[ ]:


# Step 3: Predicting the wash cycle of the given new data
mode = model.predict(input1_scaled)
print (mode[0])


# Before estimating the completion time, let's understand the washing mode and it's sequence. As shown in the table below, there are 3 types of washing mode (Daily, Cotton & Bedding), 3 sequences of the washing cycle (Wash, Rinse & Spin) in each mode, and the total time steps needed to complete each washing sequence.
# 
# ![type.png](attachment:type.png)
# 
# In the table above, assuming each time step required 6 minutes to complete. Hence, the total amount of time (in minutes) required to complete each wash sequence is calculated in the last column of the table.
# 

# For the dataset shown below, the 'index_col' column in the CSV file indicates the time step in each wash cycle (see Column C).
# 
# ![dataset.png](attachment:dataset.png)
# 
# Given that each time step takes 6 minutes to complete, index_col = 3 implies that this specific wash cycle called 'CottonWash' has completed 3 * 6 =  18 minutes of washing. Hence, the estimate time to completion for this wash sequence is 84 - 18 = 66 minutes. And the estimated time to complete the entire cotton sequence is 66 + 24 + 24 = 114 minutes. That is how long the customer will need to wait before the washing is complete.  
# 
# Using the data from 'input1.csv' file, let's estimate the total time needed for the customer to wait before she can collect her laundry.
# ***

# In[ ]:


# Step 4: Calculate the estimated time needed to wait before the sequence of washing is completed

# From the 'new_input1.csv' file, extract the time step from dataframe
t_step = input1.iloc[0, 0]  # Use proper iloc indexing

# Detaermine the total time steps to complete the entire wash sequence
if mode == ('DailyWash' or 'DailyRaise' or 'DailySpin'):
    total_steps = 13
elif mode == ('CottonWash' or 'CottonRaise' or 'CottonSpin'):
    total_steps = 22
else:
    total_steps = 17

toc = (total_steps - t_step) * 6

print ("Current wash cycle: " + mode[0])
print ("Estimate time to completion: " + str(toc) + " mins.")


# From the data collected in 'input2.csv' file, repeat $Step 4$ again to predict the wash cycle and the estimated time needed to complete the entire wash sequence.  
# 

# In[ ]:


# Insert your code here ....


# Explore using different values in 'input2.csv' to predict the new wash cycle and the new estimated time to completion.
# 

# In[ ]:


# Insert your code here ....


# ***
'''
To run this codes:
C:/Python313/python.exe "d:/Developer Courseware/__RP-C3790C-Emerging Technologies/Lesson10 - Predictive Modeling/Lab Tutorials/10.4_Estimate_Time.py"
'''