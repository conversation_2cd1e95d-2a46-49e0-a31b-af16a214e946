{"cells": [{"cell_type": "markdown", "id": "bd2d6e5a", "metadata": {"id": "bd2d6e5a"}, "source": ["# Classification Model 2: Decision Tree\n", "\n", "***\n", "### Loading Dataset"]}, {"cell_type": "code", "execution_count": 1, "id": "961450f5", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "961450f5", "executionInfo": {"status": "ok", "timestamp": 1749716856211, "user_tz": -480, "elapsed": 20192, "user": {"displayName": "<PERSON>", "userId": "08280565725279349310"}}, "outputId": "e1a721d8-774e-4dff-f3be-4c5b3443d4ff"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Mounted at /content/drive\n"]}], "source": ["from google.colab import drive\n", "\n", "drive.mount('/content/drive', force_remount=True)"]}, {"cell_type": "code", "execution_count": 2, "id": "275c6e93", "metadata": {"id": "275c6e93", "executionInfo": {"status": "ok", "timestamp": 1749716857686, "user_tz": -480, "elapsed": 9, "user": {"displayName": "<PERSON>", "userId": "08280565725279349310"}}}, "outputs": [], "source": ["ROOT_DIR = '/content/drive/MyDrive/C3790C'"]}, {"cell_type": "code", "execution_count": null, "id": "3958ce5e", "metadata": {"id": "3958ce5e", "outputId": "7d21302f-1435-4943-b31d-724998df99d6"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["     index_col       avC         avP       sdC         sdP          avR  maxC  \\\n", "432          3  3.595600  792.930000  3.332669  786.913383   399.232039  7.23   \n", "22           9  1.616815  329.370370  2.485054  583.668067   637.157143  1.76   \n", "620         14  1.305465  254.702326  2.232888  529.072646   604.123431  0.66   \n", "963          0  0.284667   34.533333  0.227744   33.000244  1429.358887  0.77   \n", "188          9  1.484593  298.003704  2.357156  558.706609   623.840883  1.84   \n", "..         ...       ...         ...       ...         ...          ...   ...   \n", "360         21  0.906095  167.071429  1.721403  405.103331   871.333092  0.09   \n", "466         15  1.292000  256.215556  2.277856  534.821437   658.162380  1.30   \n", "299          0  2.612000  574.333333  3.216506  759.213038   753.064179  7.25   \n", "493         20  1.080317  202.135000  1.919027  450.266332   678.029294  2.49   \n", "527          9  1.641481  339.711111  2.584596  619.005346   585.429776  0.59   \n", "\n", "     maxP     stdCR       stdCP         AvRR  \n", "432  1642  3.382349  805.313577   362.408577  \n", "22    271  0.587681   92.810374   449.344412  \n", "620    69  0.141624   18.216104   682.374211  \n", "963   102  0.227744   33.000244  1429.358887  \n", "188   286  0.601370   96.551553   475.380790  \n", "..    ...       ...         ...          ...  \n", "360     1  0.000000    0.000000  2628.307407  \n", "466   195  0.325882   50.023153   638.702682  \n", "299  1666  3.216506  759.213038   753.064179  \n", "493   395  0.630406  102.162728   226.295845  \n", "527    75  0.109605   15.296908   559.013457  \n", "\n", "[773 rows x 11 columns]\n", "432     CottonWash\n", "22      DailyRinse\n", "620    CottonRinse\n", "963    BeddingWash\n", "188     DailyRinse\n", "          ...     \n", "360     CottonSpin\n", "466    CottonRinse\n", "299     CottonWash\n", "493     CottonSpin\n", "527     CottonWash\n", "Name: mode, Length: 773, dtype: object\n"]}], "source": ["# Import necessary Python libraries here ...\n", "# ...\n", "# ...\n", "# ...\n", "\n", "# Loading the dataset from the /data folder here\n", "data_path  = ?\n", "\n", "# Read your csv file here ...\n", "currentdf  = ???\n", "\n", "# Allocate your training data and label\n", "x = ?\n", "y = ?\n", "\n", "# Splitting dataset into 75% for training and 25% for testing here ...\n", "# ...\n", "# ...\n", "\n", "# Display the features and label from the training set\n", "print(?)\n", "print(?)\n", "\n", "# Insert code to standardize your dataset here ...\n", "# ...\n", "# ...\n"]}, {"cell_type": "markdown", "id": "f5fa7fd8", "metadata": {"id": "f5fa7fd8"}, "source": ["## Building a Decision Tree Model\n", "\n", "Let's train a model using the selected features extracted earlier and set the 'max_depth' parameter to 3.\n"]}, {"cell_type": "code", "execution_count": null, "id": "dbdb199d", "metadata": {"id": "dbdb199d", "outputId": "0bf7702d-52b7-4545-b817-27d41860387e"}, "outputs": [{"data": {"text/plain": ["DecisionTreeClassifier(max_depth=3)"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["from sklearn.tree import DecisionTreeClassifier\n", "class_tree = DecisionTreeClassifier(max_depth=??)\n", "class_tree.fit(X_train, y_train)\n"]}, {"cell_type": "markdown", "id": "670a4bcd", "metadata": {"id": "670a4bcd"}, "source": ["We vistualize the classification tree by importing 'export_graphviz', which exports the decision tree in a file with DOT format. This function generates a GraphiViz representation of the decision tree, which is then written into 'out_file'. Lasty, the image function is used to display the tree."]}, {"cell_type": "code", "execution_count": null, "id": "dee361b4", "metadata": {"id": "dee361b4"}, "outputs": [], "source": ["# Import necessary libraries\n", "# ...\n"]}, {"cell_type": "markdown", "id": "0b62ca6a", "metadata": {"id": "0b62ca6a"}, "source": ["The code to display the graphical representation is as follows:"]}, {"cell_type": "code", "execution_count": null, "id": "c3084c6c", "metadata": {"id": "c3084c6c", "outputId": "e9b0cf7e-1965-426d-e592-4e9b16d8cde6"}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<IPython.core.display.Image object>"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["dot_data = StringIO()\n", "export_graphviz(decision_tree=??,\n", "                out_file=dot_data,\n", "                rounded=True,\n", "                feature_names = X_train.columns,\n", "                class_names = ['CottonW<PERSON>', 'CottonRinse', 'CottonS<PERSON>', 'BeddingWash', 'BeddingRinse', 'BeddingSpin',\n", "                 'DailyWash', 'DailyRinse','DailySpin'],\n", "                special_characters=True)\n", "graph = pydotplus.graph_from_dot_data(dot_data.getvalue())\n", "Image(graph.create_png())\n"]}, {"cell_type": "markdown", "id": "96d0ce8c", "metadata": {"id": "96d0ce8c"}, "source": ["Next, print the 'proportions' by setting the parameter for $proportion=True$.\n"]}, {"cell_type": "code", "execution_count": null, "id": "84c96d98", "metadata": {"id": "84c96d98", "outputId": "90b14cd0-5094-4a76-d1d7-83614a4bef61"}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<IPython.core.display.Image object>"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["dot_data = StringIO()\n", "export_graphviz(decision_tree=??,\n", "                out_file=dot_data,\n", "                rounded=True,\n", "                proportion=??,\n", "                feature_names = X_train.columns,\n", "                class_names = ['CottonW<PERSON>', 'CottonRinse', 'CottonS<PERSON>', 'BeddingWash', 'BeddingRinse', 'BeddingSpin',\n", "                 'DailyWash', 'DailyRinse','DailySpin'],\n", "                special_characters=True)\n", "graph = pydotplus.graph_from_dot_data(dot_data.getvalue())\n", "Image(graph.create_png())\n"]}, {"cell_type": "markdown", "id": "fc0d8dc6", "metadata": {"id": "fc0d8dc6"}, "source": ["Has the tree structure changed? Are you getting a different result now? Explain your observation."]}, {"cell_type": "markdown", "id": "f157cdd3", "metadata": {"id": "f157cdd3"}, "source": ["...\n"]}, {"cell_type": "markdown", "id": "5b1a4381", "metadata": {"id": "5b1a4381"}, "source": ["## Training a Large Classification Tree\n", "\n", "Let's train a larger tree by modifying the 'max_depth' to 6 and 'min_samples_split' to 50 in the scikit-learn parameters. You may also refer to https://scikit-learn.org/stable/modules/generated/sklearn.tree.DecisionTreeClassifier.html for other types of parameters."]}, {"cell_type": "code", "execution_count": null, "id": "933c0f07", "metadata": {"id": "933c0f07"}, "outputs": [], "source": ["class_tree = DecisionTreeClassifier(max_depth=??, min_samples_split=??)\n", "class_tree.fit(??, ??)\n", "y_pred_class_tree = class_tree.predict(??)\n"]}, {"cell_type": "markdown", "id": "44bc5b4d", "metadata": {"id": "44bc5b4d"}, "source": ["We can calculate the training accuracy score of this model as follows:"]}, {"cell_type": "code", "execution_count": null, "id": "47db3811", "metadata": {"id": "47db3811", "outputId": "da2eb750-b275-4d78-f250-c477e631c5f8"}, "outputs": [{"data": {"text/plain": ["0.8913324708926261"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["from sklearn.metrics import accuracy_score\n", "accuracy_class_tree = accuracy_score(y_true=??, y_pred=??)\n", "accuracy_class_tree\n"]}, {"cell_type": "markdown", "id": "72e3b4dc", "metadata": {"id": "72e3b4dc"}, "source": ["We get 0.89133 or 89.1% accuracy for this $Decison Tree$ model by using the training data, which is much higher than the KNN model (81.4%).\n"]}, {"cell_type": "markdown", "id": "f4d91f7c", "metadata": {"id": "f4d91f7c"}, "source": ["Another nice feature of this model is that we can get a normalizd score of \"important\" features by using the 'feature_importances_' method."]}, {"cell_type": "code", "execution_count": null, "id": "39fa3d66", "metadata": {"id": "39fa3d66", "outputId": "0f892ec2-da00-4a8a-d967-d547213b16e7"}, "outputs": [{"data": {"text/plain": ["index_col    0.651\n", "sdP          0.183\n", "maxC         0.059\n", "maxP         0.055\n", "AvRR         0.030\n", "stdCP        0.010\n", "avP          0.010\n", "avC          0.001\n", "sdC          0.000\n", "avR          0.000\n", "stdCR        0.000\n", "dtype: float64"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["pd.Series(data=class_tree.feature_importances_, index=X_train.columns).sort_values(ascending=False).round(3)\n"]}, {"cell_type": "markdown", "id": "fc1831e6", "metadata": {"id": "fc1831e6"}, "source": ["You can also compare these values using a bar graph."]}, {"cell_type": "code", "execution_count": null, "id": "c9b80d43", "metadata": {"id": "c9b80d43", "outputId": "afd798d8-e9ef-4581-867c-504fed230492"}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["pd.Series(data=class_tree.feature_importances_, index=X_train.columns).sort_values(ascending=False).plot(kind='bar');\n"]}, {"cell_type": "markdown", "id": "058b6c4c", "metadata": {"id": "058b6c4c"}, "source": ["Discuss with your team what can you tell from these feature importance?"]}, {"cell_type": "markdown", "id": "5e884a7d", "metadata": {"id": "5e884a7d"}, "source": ["Your response here ...\n", "\n"]}, {"cell_type": "markdown", "id": "bee5bac9", "metadata": {"id": "bee5bac9"}, "source": ["## Model Evaluation\n", "\n", "### Confusion Matrix\n", "\n", "Let's compute the confusion matrix for this Decision Tree model for the laudromat use case.\n"]}, {"cell_type": "code", "execution_count": null, "id": "2b00a257", "metadata": {"id": "2b00a257", "outputId": "4724aa9e-d4f1-4da0-b1e0-f36596378ed7"}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["from sklearn.metrics import confusion_matrix\n", "\n", "# Determine the accuracy of the model using test data\n", "score = class_tree.score(??, ??)\n", "\n", "# Provide the necessary label\n", "class_label=??\n", "\n", "class_tree.fit(??, ??)\n", "cm = confusion_matrix(y_test, class_tree.predict(??), labels=??)\n", "\n", "axes = sns.heatmap(cm, square=True, annot=True, fmt ='d', cbar=True, cmap=plt.cm.GnBu)\n", "axes.set_ylabel('Actual')\n", "axes.set_ylabel('Predication')\n", "tick_marks = np.arange(len(class_label)) + 0.5\n", "axes.set_xticks(tick_marks)\n", "axes.set_xticklabels(class_label, rotation = 90)\n", "axes.set_yticks(tick_marks)\n", "axes.set_yticklabels(class_label, rotation = 0)\n", "axes.set_title('Confusion Matrix')\n", "plt.show()\n"]}, {"cell_type": "markdown", "id": "42169fd2", "metadata": {"id": "42169fd2"}, "source": ["Let's calculate the accuracy, precision, recall and F1 Score for our Decision Tree model using the test dataset.\n"]}, {"cell_type": "code", "execution_count": null, "id": "8f5969c4", "metadata": {"id": "8f5969c4", "outputId": "75426699-8b72-42a8-b0ed-d10b5e958b1f"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Accuracy: 83.3%, Precision: 84.6%, Recall: 89.5%, F1 Score: 85.6%\n"]}], "source": ["from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score\n", "\n", "class_tree = DecisionTreeClassifier(max_depth=??, min_samples_split=??)\n", "class_tree.fit(??, ??)\n", "\n", "# Get prediction using test data\n", "y_pred_test = class_tree.predict(??)\n", "\n", "# Calculate the relevant metrics\n", "accuracy = accuracy_score(??, ??)\n", "precision = precision_score(??, ??, average='macro')\n", "recall = recall_score(??, ??, average='macro')\n", "f1 = f1_score(??, ??, average='macro')\n", "\n", "print (\"Accuracy: {:0.1f}%, Precision: {:0.1f}%, Recall: {:0.1f}%, F1 Score: {:0.1f}%\".format(100*accuracy, 100* precision, 100*recall, 100*f1))\n"]}, {"cell_type": "markdown", "id": "d6b86d46", "metadata": {"id": "d6b86d46"}, "source": ["Are the accuracy, precision, recall and F1 Score for Decision Tree model better or worst then your KNN model? Explain.\n"]}, {"cell_type": "markdown", "id": "985a83dc", "metadata": {"id": "985a83dc"}, "source": ["Your response here ...\n"]}, {"cell_type": "markdown", "id": "b67a0868", "metadata": {"id": "b67a0868"}, "source": ["Lastly, you may save your Decision Tree model for future comparison.\n"]}, {"cell_type": "code", "execution_count": null, "id": "89f322b2", "metadata": {"id": "89f322b2", "outputId": "f2936994-3665-428d-801a-999c196bc305"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Model Saved\n"]}], "source": ["import pickle as pk\n", "\n", "model_filename= ROOT_DIR + \"/model/dt.mdl\"\n", "with open(model_filename, \"wb\") as file:\n", "    pk.dump(??, file)\n", "print(\"Model Saved\")\n"]}, {"cell_type": "markdown", "id": "2266bace", "metadata": {"id": "2266bace"}, "source": ["***"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}, "colab": {"provenance": []}}, "nbformat": 4, "nbformat_minor": 5}